<!DOCTYPE html>
<html>
<head>
    <title>Test Button Timeout</title>
</head>
<body>
    <h1>Test Button Timeout Behavior</h1>
    <p>This is a simple test to verify the button timeout behavior works correctly.</p>
    
    <div id="test-results">
        <h2>Test Instructions:</h2>
        <ol>
            <li>Open the team management page in your application</li>
            <li>Find a member with "RESEND INVITE" button</li>
            <li>Click the "RESEND INVITE" button</li>
            <li>Verify the button text changes to "INVITATION RESENT" and becomes disabled</li>
            <li>Try to trigger a table re-render (e.g., by searching or changing filters)</li>
            <li>Verify the button remains disabled and shows "INVITATION RESENT"</li>
            <li>Wait for 60 seconds (or check console logs)</li>
            <li>Verify the button returns to "RESEND INVITE" and becomes enabled</li>
        </ol>
        
        <h2>Expected Console Logs:</h2>
        <pre>
ActionLinkButton render for member [ID]: isDisabled=false, text="RESEND INVITE"
ActionLinkButton: Starting timeout for member [ID]
ActionLinkButton render for member [ID]: isDisabled=true, text="INVITATION RESENT"
... (after 60 seconds) ...
ActionLinkButton: Timeout completed for member [ID]
ActionLinkButton render for member [ID]: isDisabled=false, text="RESEND INVITE"
        </pre>
        
        <h2>What We Fixed:</h2>
        <ul>
            <li><strong>Problem:</strong> Button timeout was resetting on table re-renders</li>
            <li><strong>Root Cause:</strong> Component recreation and callback function recreation</li>
            <li><strong>Solution:</strong> 
                <ul>
                    <li>Global state management for button timeouts per member ID</li>
                    <li>Memoized components with stable comparison</li>
                    <li>useCallback for parent component functions</li>
                    <li>useMemo for table data generation</li>
                </ul>
            </li>
        </ul>
    </div>
</body>
</html>
