import { useTheme } from "@emotion/react";
import {
  RDS<PERSON><PERSON>pography,
  RDSButton,
  RDSSearchInput,
  RDSTagInteractive,
  AppTheme,
  RDSTable,
  RDSAssetWrapper,
  RDSToast,
} from "@roshn/ui-kit";
import { useCallback, useEffect, useMemo, useState } from "react";
import { styles } from "./styles";
import { teamMembersTagData, MemberData } from "./constants";
import { useTeamMemberList } from "~/services/team-members/hooks/use-team-member-list";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import { createSvg } from "~/components/svgs";
import InviteMemberModal from "./invite-member-modal";
import { useGetMyInfo } from "~/services/team-members/hooks/use-my-info";
import { useRoleBasedColumns, UseRoleBasedTable } from "./hooks/use-role-based-table";
import LeaveOrRemoveModal from "./leave-or-remove-modal";
import { useResendInvite } from "~/services/team-members/hooks/use-resend-invite";

export default function TeamManagementPage() {
  const theme = useTheme() as AppTheme;
  const TeamManagementIcon = createSvg(() => import("~/assets/icons/invite-member.svg"));
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [results, setResults] = useState<MemberData[]>([]);
  const [activeTag, setActiveTag] = useState("All");
  const [showLeaveOrRemoveModal, setShowLeaveOrRemoveModal] = useState(false);
  const [modalAction, setModalAction] = useState<{
    type: "REMOVE" | "CANCEL_INVITE" | "LEAVE_TEAM";
    memberData: MemberData | null;
  }>({ type: "REMOVE", memberData: null });
  const [showResentToast, setShowResentToast] = useState({
    email: "",
    isShow: false,
  });

  const [search, setSearch] = useState({ searchQuery: "" });
  const [searchParam, setSearchParam] = useState({ search: "" });
  const { data: myData, isFetching: isMyDataFetching } = useGetMyInfo();

  const { data, isFetching, isError } = useTeamMemberList({});

  const { mutateAsync: resendInvite, isPending: isResendPending } = useResendInvite({});

  const handleOpenModal = useCallback((
    actionType: "REMOVE" | "CANCEL_INVITE" | "LEAVE_TEAM",
    memberData: MemberData,
  ) => {
    setModalAction({ type: actionType, memberData });
    setShowLeaveOrRemoveModal(true);
  }, []);

  useEffect(() => {
    if (data) {
      const userData = Object.values(data)
        .filter((value) => value)
        .map((value) => value as MemberData);
      setResults(userData);
    }
  }, [data]);

  const getTeamMemberTableColumn = useCallback(() => {
    const memberTableColumn = useRoleBasedColumns(myData?.role, theme);
    return memberTableColumn || [];
  }, [myData?.role, theme]);

  const handleResendInvite = useCallback((memberData: MemberData) => {
    resendInvite({ memberId: memberData.id }, {
      onSuccess: () => {
        setShowResentToast({ email: memberData.email, isShow: true });
      },
    });
  }, [resendInvite]);

  const tableData = useMemo(() => UseRoleBasedTable(
    data?.results || [],
    myData?.role,
    theme,
    handleOpenModal,
    handleResendInvite,
  ), [data?.results, myData?.role, theme, handleOpenModal, handleResendInvite]);

  return (
    <>
      {showResentToast.isShow && (
        <RDSToast
          appearance="success"
          leadIcon
          label={`Invitation resent to ${showResentToast.email}`}
          isDismissible
          position="bottom-center"
        />
      )}
      <LeaveOrRemoveModal
        showModal={showLeaveOrRemoveModal}
        onClose={() => setShowLeaveOrRemoveModal(false)}
        actionType={modalAction.type}
        memberData={modalAction.memberData}
      />
      <InviteMemberModal showModal={showInviteModal} onClose={() => setShowInviteModal(false)} />
      <div css={styles.wrapper}>
        <div css={styles.headerContainer}>
          <div css={styles.header()}>
            <div>
              <RDSTypography fontName={theme?.rds?.typographies?.display?.d5}>
                Team members
              </RDSTypography>
            </div>
            <div>
              <RDSButton
                onClick={() => setShowInviteModal(true)}
                size="lg"
                text="INVITE MEMBER"
                leadIcon={
                  <RDSAssetWrapper>
                    <TeamManagementIcon />
                  </RDSAssetWrapper>
                }
              />
            </div>
          </div>
          <div>
            <RDSSearchInput
              type="text"
              placeholder="Search by name or email..."
              css={styles.searchInput}
              onChange={(e) => {
                setSearch({ searchQuery: e.target.value });
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  setSearchParam({ search: search.searchQuery });
                }
              }}
              onClearSearchInput={() => {
                setSearch({ searchQuery: "" });
                setSearchParam({ search: "" });
              }}
              value={search.searchQuery}
            />
            <div css={styles.tagContainer}>
              {teamMembersTagData.map((tag) => (
                <RDSTagInteractive
                  key={tag.label}
                  size="md"
                  label={tag.label}
                  state={tag.label === activeTag ? "active" : "default"}
                  // onClick={() => setActiveTag(tag.label)}
                />
              ))}
            </div>
          </div>
        </div>
        {isFetching || isMyDataFetching ? (
          <div css={styles.loaderWrapper}>
            <RoshnContainerLoader />
          </div>
        ) : (
          !isError && <RDSTable columns={getTeamMemberTableColumn()} data={tableData} />
        )}
      </div>
    </>
  );
}
