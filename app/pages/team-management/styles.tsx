import { css } from "@emotion/react";
import { AppTheme } from "@roshn/ui-kit";

export const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      padding: theme.rds.dimension["600"],
      background: theme?.rds?.color?.background?.brand?.secondary?.inverse?.default,
      minHeight: "100vh",
      gap: theme.rds.dimension["200"],
    }),
  emptyStateWrapper: (theme: AppTheme) =>
    css({
      margin: "auto",
      marginTop: "0",
      padding: `${theme?.rds.dimension[500]} 0`,
    }),
  headerContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),
  header: () =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      alignContent: "center",
    }),
  headerWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      alignItems: "center",
      justifyContent: "space-between",
    }),  
  searchInput: (theme: AppTheme) =>
    css({
      gap: theme.rds.dimension["200"],
    }),
  leadImage: () =>
    css({
      width: "5.5rem",
      height: "1.75rem",
    }),
  tagContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      marginTop: theme.rds.dimension["300"],
    }),
  loaderWrapper: css({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "50vh",
  }),
  nameColumn: css({
    display: "flex",
    gap: "0.5rem",
    alignItems: "center",
  }),
  nameAvatar: (theme: AppTheme) =>
    css({
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      height: "2.5rem",
      width: "2.5rem",
      borderRadius: theme?.rds?.border.borderRadius.round,
      background: theme?.rds?.color.background.functional.success.darker.default,
      color: "white"
    }),
  fieldColor: (theme: AppTheme) =>
    css({
      color: theme?.rds?.color.text.ui.tertiary,
    }),
  modalDimension: css({
    "& div": {
      "& div": {
        maxWidth: "640px",
      },
    },
    "& p": {
      margin: 0,
    },
  }),
};
