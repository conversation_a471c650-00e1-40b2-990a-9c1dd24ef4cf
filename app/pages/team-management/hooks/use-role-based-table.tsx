import { ChevronDownIcon } from "@radix-ui/react-icons";
import { AppTheme, RDSLinkButton, RDSSelect, RDSTypography } from "@roshn/ui-kit";
import { AssetWrapper } from "node_modules/@roshn/ui-kit/dist/components/rds-components/asset-wrapper";
import { styles } from "../styles";
import { snakeToCapitalizedWords } from "~/utils/casing-util";
import { getInitials, MemberData, roleOptions } from "../constants";
import { useButtonTimeout } from "./use-button-timeout";

export enum MemberStatus {
  INVITATION_SENT = "INVITATION_SENT",
  ACTIVE = "ACTIVE",
}

export enum MemberRole {
  OWNER = "OWNER",
  ADMIN = "ADMIN",
  STAFF = "STAFF",
}

export const getRoleBasedLink = (memberData: MemberData) => {
  if (memberData?.role === MemberRole.ADMIN) {
    return "LEAVE TEAM";
  } else if (memberData?.role === MemberRole.STAFF) {
    return "REMOVE";
  }
  return "CANCEL INVITE";
};

export const getRoleBasedLinkButton = (memberData: MemberData) => {
  return memberData?.status === MemberStatus.INVITATION_SENT
    ? "CANCEL INVITE"
    : getRoleBasedLink(memberData);
};

const ActionLinkButton = ({
  memberData,
  onResendInvite,
}: {
  memberData: MemberData;
  onResendInvite?: (
    memberData: MemberData,
  ) => void;
}) => {

  const originalText = "RESEND INVITE";
  const { isDisabled, buttonText, handleClick } = useButtonTimeout(
    originalText,
    "INVITATION RESENT",
  );

  return (
    <RDSLinkButton
      text={buttonText}
      disabled={isDisabled}
      onClick={() => handleClick(() => onResendInvite?.(memberData))}
    />
  );
};

export const getComponentMap = (
  theme: AppTheme,
  onOpenLeaveOrRemoveModal?: (
    actionType: "REMOVE" | "CANCEL_INVITE" | "LEAVE_TEAM",
    memberData: MemberData,
  ) => void,
  onResendInvite?: (memberData: MemberData) => void,
) => ({
  name: (memberData: MemberData) => (
    <div css={styles.nameColumn}>
      <div css={styles.nameAvatar(theme)}>{getInitials(memberData?.name)}</div>
      <RDSTypography>{memberData?.name}</RDSTypography>
    </div>
  ),
  email: (memberData: MemberData) => <RDSTypography>{memberData?.email}</RDSTypography>,
  nonEditableRole: (memberData: MemberData) => (
    <RDSTypography>{snakeToCapitalizedWords(memberData?.role)}</RDSTypography>
  ),
  linkButton: (memberData: MemberData) => {
    const getActionType = (): "REMOVE" | "CANCEL_INVITE" | "LEAVE_TEAM" => {
      if (memberData?.status === MemberStatus.INVITATION_SENT) {
        return "CANCEL_INVITE";
      }
      if (memberData?.role === MemberRole.ADMIN) {
        return "LEAVE_TEAM";
      }
      return "REMOVE";
    };

    return (
      <RDSLinkButton
        text={getRoleBasedLinkButton(memberData)}
        onClick={() => onOpenLeaveOrRemoveModal?.(getActionType(), memberData)}
      />
    );
  },
  custom: (memberData: MemberData) =>
    showRoleDropDownOrLink(memberData) === "select" ? (
      <RDSSelect
        options={roleOptions}
        defaultValue={
          memberData?.role
            ? {
                value: memberData.role,
                label: snakeToCapitalizedWords(memberData.role),
              }
            : null
        }
        isDisabled={memberData?.role === MemberRole.OWNER}
      />
    ) : (
      <ActionLinkButton memberData={memberData} onResendInvite={onResendInvite} />
    ),
});

export const showRoleDropDownOrLink = (memberData: MemberData) => {
  return memberData?.status === MemberStatus.ACTIVE ? "select" : "linkButton";
};

export const roleBasedColumns = () => {
  return [
    {
      role: "OWNER",
      access: [
        {
          label: "Name",
          isSortable: true,
        },
        { label: "Email", isSortable: true },
        { label: "Role", isSortable: true },
        { label: "Actions", isSortable: false },
      ],
    },
    {
      role: "ADMIN",
      access: [
        {
          label: "Name",
          isSortable: true,
        },
        { label: "Email", isSortable: true },
        { label: "Role", isSortable: true },
        { label: "Actions", isSortable: false },
      ],
    },
    {
      role: "STAFF",
      access: [
        {
          label: "Name",
          isSortable: true,
        },
        { label: "Email", isSortable: true },
        { label: "Role", isSortable: true },
      ],
    },
  ];
};

const commonColumns = [
  {
    label: "Name",
    content: "name",
  },
  {
    label: "Email",
    content: "email",
  },
];

export const roleBasedColumnContent = () => {
  return [
    {
      role: "OWNER",
      memberRole: "OWNER",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: true,
        },
        {
          label: "Actions",
          content: null,
        },
      ],
    },
    {
      role: "OWNER",
      memberRole: "ADMIN",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: false,
        },
        {
          label: "Actions",
          content: "linkButton",
        },
      ],
    },
    {
      role: "OWNER",
      memberRole: "STAFF",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: false,
        },
        {
          label: "Actions",
          content: "linkButton",
        },
      ],
    },
    {
        role: "ADMIN",
        memberRole: "OWNER",
        content: [
          ...commonColumns,
          {
            label: "Role",
            content: "custom",
            disabled: true,
          },
          {
            label: "Actions",
            content: null,
          },
        ],
      },
    {
      role: "ADMIN",
      memberRole: "ADMIN",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: true,
        },
        {
          label: "Actions",
          content: null,
        },
      ],
    },
    {
      role: "ADMIN",
      memberRole: "STAFF",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: false,
        },
        {
          label: "Actions",
          content: "linkButton",
        },
      ],
    },
    {
      role: "STAFF",
      memberRole: "OWNER",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "nonEditableRole",
        },
      ],
    },
    {
      role: "STAFF",
      memberRole: "ADMIN",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "nonEditableRole",
        },
      ],
    },
    {
      role: "STAFF",
      memberRole: "STAFF",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "nonEditableRole",
        },
      ],
    },
  ];
};

export const useRoleBasedColumns = (role: string = "OWNER", theme: AppTheme) => {
  const columnNames = roleBasedColumns().find((column) => column.role === role);
  return columnNames?.access.map((column) => ({
    id: column.label,
    header: (
      <div css={styles.headerWrapper}>
        <RDSTypography isBold fontName={theme?.rds?.typographies?.label?.emphasis?.md}>
          {column.label}
        </RDSTypography>
        {column.isSortable && (
          <AssetWrapper size="20px" type="round">
            <ChevronDownIcon color="green" />
          </AssetWrapper>
        )}
      </div>
    ),
    accessor: column.label,
  }));
};

export const UseRoleBasedTable = (
  results: MemberData[],
  role: String = "OWNER",
  theme: AppTheme,
  onOpenLeaveOrRemoveModal?: (
    actionType: "REMOVE" | "CANCEL_INVITE" | "LEAVE_TEAM",
    memberData: MemberData,
  ) => void,
  onResendInvite?: (memberData: MemberData) => void,
) => {
  if (!results) {
    return [];
  }
  const componentMap = getComponentMap(theme, onOpenLeaveOrRemoveModal, onResendInvite);

  return results.map((memberData: MemberData, index: number) => {
    const config = roleBasedColumnContent().find(
      (config) => config.role === role && config.memberRole === memberData.role,
    );

    if (!config) {
      return {
        id: `row-${index}`,
        Name: { component: null },
        Email: { component: null },
        Role: { component: null },
        Actions: { component: null },
      };
    }

    return {
      id: `row-${index}`,
      ...config.content.reduce(
        (acc, columnData) => {
          acc[columnData.label] = {
            component:
              columnData.content && columnData.content in componentMap
                ? componentMap[columnData.content as keyof typeof componentMap](memberData)
                : null,
          };
          return acc;
        },
        {} as Record<string, any>,
      ),
    };
  });
};
