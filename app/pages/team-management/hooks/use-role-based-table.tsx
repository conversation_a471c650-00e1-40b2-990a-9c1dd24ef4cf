import { ChevronDownIcon } from "@radix-ui/react-icons";
import { AppTheme, RDSLinkButton, RDSSelect, RDSTypography } from "@roshn/ui-kit";
import { AssetWrapper } from "node_modules/@roshn/ui-kit/dist/components/rds-components/asset-wrapper";
import React, { useMemo, useState, useRef, useEffect } from "react";
import { styles } from "../styles";
import { snakeToCapitalizedWords } from "~/utils/casing-util";
import { getInitials, MemberData, roleOptions } from "../constants";

// Global state to track button timeouts per member ID
const buttonTimeouts = new Map<string, { isDisabled: boolean; timeoutId?: NodeJS.Timeout }>();

export enum MemberStatus {
  INVITATION_SENT = "INVITATION_SENT",
  ACTIVE = "ACTIVE",
}

export enum MemberRole {
  OWNER = "OWNER",
  ADMIN = "ADMIN",
  STAFF = "STAFF",
}

export const getRoleBasedLink = (memberData: MemberData) => {
  if (memberData?.role === MemberRole.ADMIN) {
    return "LEAVE TEAM";
  } else if (memberData?.role === MemberRole.STAFF) {
    return "REMOVE";
  }
  return "CANCEL INVITE";
};

export const getRoleBasedLinkButton = (memberData: MemberData) => {
  return memberData?.status === MemberStatus.INVITATION_SENT
    ? "CANCEL INVITE"
    : getRoleBasedLink(memberData);
};

const ActionLinkButton = React.memo(({
  memberData,
  onResendInvite,
}: {
  memberData: MemberData;
  onResendInvite?: (
    memberData: MemberData,
  ) => void;
}) => {
  const memberId = memberData.id;
  const [, forceUpdate] = useState({});
  const forceUpdateRef = useRef(() => forceUpdate({}));

  // Get current state from global map
  const currentState = buttonTimeouts.get(memberId) || { isDisabled: false };
  const isDisabled = currentState.isDisabled;
  const buttonText = isDisabled ? "INVITATION RESENT" : "RESEND INVITE";

  console.log(`ActionLinkButton render for member ${memberId}: isDisabled=${isDisabled}, text="${buttonText}"`);

  const handleClick = () => {
    if (isDisabled) return;

    console.log(`ActionLinkButton: Starting timeout for member ${memberId}`);

    // Execute the callback
    onResendInvite?.(memberData);

    // Clear any existing timeout
    const existingState = buttonTimeouts.get(memberId);
    if (existingState?.timeoutId) {
      clearTimeout(existingState.timeoutId);
    }

    // Set disabled state
    const timeoutId = setTimeout(() => {
      console.log(`ActionLinkButton: Timeout completed for member ${memberId}`);
      buttonTimeouts.set(memberId, { isDisabled: false });
      forceUpdateRef.current();
    }, 60000);

    buttonTimeouts.set(memberId, { isDisabled: true, timeoutId });
    forceUpdateRef.current();
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      const state = buttonTimeouts.get(memberId);
      if (state?.timeoutId) {
        clearTimeout(state.timeoutId);
        buttonTimeouts.delete(memberId);
      }
    };
  }, [memberId]);

  return (
    <RDSLinkButton
      text={buttonText}
      disabled={isDisabled}
      onClick={handleClick}
    />
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  // Only re-render if the member ID or email changes (which would indicate a different member)
  return prevProps.memberData.id === nextProps.memberData.id &&
         prevProps.memberData.email === nextProps.memberData.email;
});

// Add display name for better debugging
ActionLinkButton.displayName = 'ActionLinkButton';

export const getComponentMap = (
  theme: AppTheme,
  onOpenLeaveOrRemoveModal?: (
    actionType: "REMOVE" | "CANCEL_INVITE" | "LEAVE_TEAM",
    memberData: MemberData,
  ) => void,
  onResendInvite?: (memberData: MemberData) => void,
) => ({
  name: (memberData: MemberData) => (
    <div css={styles.nameColumn}>
      <div css={styles.nameAvatar(theme)}>{getInitials(memberData?.name)}</div>
      <RDSTypography>{memberData?.name}</RDSTypography>
    </div>
  ),
  email: (memberData: MemberData) => <RDSTypography>{memberData?.email}</RDSTypography>,
  nonEditableRole: (memberData: MemberData) => (
    <RDSTypography>{snakeToCapitalizedWords(memberData?.role)}</RDSTypography>
  ),
  linkButton: (memberData: MemberData) => {
    const getActionType = (): "REMOVE" | "CANCEL_INVITE" | "LEAVE_TEAM" => {
      if (memberData?.status === MemberStatus.INVITATION_SENT) {
        return "CANCEL_INVITE";
      }
      if (memberData?.role === MemberRole.ADMIN) {
        return "LEAVE_TEAM";
      }
      return "REMOVE";
    };

    return (
      <RDSLinkButton
        text={getRoleBasedLinkButton(memberData)}
        onClick={() => onOpenLeaveOrRemoveModal?.(getActionType(), memberData)}
      />
    );
  },
  custom: (memberData: MemberData) =>
    showRoleDropDownOrLink(memberData) === "select" ? (
      <RDSSelect
        key={`select-${memberData.id}`}
        options={roleOptions}
        defaultValue={
          memberData?.role
            ? {
                value: memberData.role,
                label: snakeToCapitalizedWords(memberData.role),
              }
            : null
        }
        isDisabled={memberData?.role === MemberRole.OWNER}
      />
    ) : (
      <ActionLinkButton
        key={`action-${memberData.id}`}
        memberData={memberData}
        onResendInvite={onResendInvite}
      />
    ),
});

export const showRoleDropDownOrLink = (memberData: MemberData) => {
  return memberData?.status === MemberStatus.ACTIVE ? "select" : "linkButton";
};

export const roleBasedColumns = () => {
  return [
    {
      role: "OWNER",
      access: [
        {
          label: "Name",
          isSortable: true,
        },
        { label: "Email", isSortable: true },
        { label: "Role", isSortable: true },
        { label: "Actions", isSortable: false },
      ],
    },
    {
      role: "ADMIN",
      access: [
        {
          label: "Name",
          isSortable: true,
        },
        { label: "Email", isSortable: true },
        { label: "Role", isSortable: true },
        { label: "Actions", isSortable: false },
      ],
    },
    {
      role: "STAFF",
      access: [
        {
          label: "Name",
          isSortable: true,
        },
        { label: "Email", isSortable: true },
        { label: "Role", isSortable: true },
      ],
    },
  ];
};

const commonColumns = [
  {
    label: "Name",
    content: "name",
  },
  {
    label: "Email",
    content: "email",
  },
];

export const roleBasedColumnContent = () => {
  return [
    {
      role: "OWNER",
      memberRole: "OWNER",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: true,
        },
        {
          label: "Actions",
          content: null,
        },
      ],
    },
    {
      role: "OWNER",
      memberRole: "ADMIN",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: false,
        },
        {
          label: "Actions",
          content: "linkButton",
        },
      ],
    },
    {
      role: "OWNER",
      memberRole: "STAFF",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: false,
        },
        {
          label: "Actions",
          content: "linkButton",
        },
      ],
    },
    {
        role: "ADMIN",
        memberRole: "OWNER",
        content: [
          ...commonColumns,
          {
            label: "Role",
            content: "custom",
            disabled: true,
          },
          {
            label: "Actions",
            content: null,
          },
        ],
      },
    {
      role: "ADMIN",
      memberRole: "ADMIN",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: true,
        },
        {
          label: "Actions",
          content: null,
        },
      ],
    },
    {
      role: "ADMIN",
      memberRole: "STAFF",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "custom",
          disabled: false,
        },
        {
          label: "Actions",
          content: "linkButton",
        },
      ],
    },
    {
      role: "STAFF",
      memberRole: "OWNER",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "nonEditableRole",
        },
      ],
    },
    {
      role: "STAFF",
      memberRole: "ADMIN",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "nonEditableRole",
        },
      ],
    },
    {
      role: "STAFF",
      memberRole: "STAFF",
      content: [
        ...commonColumns,
        {
          label: "Role",
          content: "nonEditableRole",
        },
      ],
    },
  ];
};

export const useRoleBasedColumns = (role: string = "OWNER", theme: AppTheme) => {
  const columnNames = roleBasedColumns().find((column) => column.role === role);
  return columnNames?.access.map((column) => ({
    id: column.label,
    header: (
      <div css={styles.headerWrapper}>
        <RDSTypography isBold fontName={theme?.rds?.typographies?.label?.emphasis?.md}>
          {column.label}
        </RDSTypography>
        {column.isSortable && (
          <AssetWrapper size="20px" type="round">
            <ChevronDownIcon color="green" />
          </AssetWrapper>
        )}
      </div>
    ),
    accessor: column.label,
  }));
};

export const useRoleBasedTable = (
  results: MemberData[],
  role: String = "OWNER",
  theme: AppTheme,
  onOpenLeaveOrRemoveModal?: (
    actionType: "REMOVE" | "CANCEL_INVITE" | "LEAVE_TEAM",
    memberData: MemberData,
  ) => void,
  onResendInvite?: (memberData: MemberData) => void,
) => {
  const componentMap = useMemo(
    () => getComponentMap(theme, onOpenLeaveOrRemoveModal, onResendInvite),
    [theme, onOpenLeaveOrRemoveModal, onResendInvite]
  );

  return useMemo(() => {
    if (!results) {
      return [];
    }

    return results.map((memberData: MemberData) => {
      const config = roleBasedColumnContent().find(
        (config) => config.role === role && config.memberRole === memberData.role,
      );

      if (!config) {
        return {
          Name: { component: null },
          Email: { component: null },
          Role: { component: null },
          Actions: { component: null },
        };
      }

      return config.content.reduce(
        (acc, columnData) => {
          acc[columnData.label] = {
            component:
              columnData.content && columnData.content in componentMap
                ? componentMap[columnData.content as keyof typeof componentMap](memberData)
                : null,
          };
          return acc;
        },
        {} as Record<string, any>,
      );
    });
  }, [results, role, componentMap]);
};

// Keep the old function name for backward compatibility
export const UseRoleBasedTable = useRoleBasedTable;
