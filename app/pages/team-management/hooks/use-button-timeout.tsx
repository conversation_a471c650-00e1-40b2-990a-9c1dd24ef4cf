import { useState } from "react";
import { useTimeout } from "~/hooks/use-timeout";

export const useButtonTimeout = (originalText: string, clickedText: string = "Processing...") => {
  const [isClicked, setIsClicked] = useState(false);
  const [buttonText, setButtonText] = useState(originalText);

  useTimeout(
    () => {
      setIsClicked(false);
      setButtonText(originalText);
    },
    isClicked ? 60000 : null,
  );

  const handleClick = (originalCallback: () => void) => {
    if (isClicked) return;
    originalCallback();
    setIsClicked(true);
    setButtonText(clickedText);
  };

  console.log(buttonText, "buttonText");
  return {
    isDisabled: isClicked,
    buttonText,
    handleClick,
  };
};
