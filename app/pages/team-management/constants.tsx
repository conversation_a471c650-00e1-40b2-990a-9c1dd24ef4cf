export enum MemberStatus {
  INVITATION_SENT = "INVITATION_SENT",
  ACTIVE = "ACTIVE",
}

export enum MemberRole {
  OWNER = "OWNER",
  ADMIN = "ADMIN",
  STAFF = "STAFF",
}

export interface MemberData {
  name: string;
  email: string;
  role: MemberRole;
  status: MemberStatus;
  id: string;
}

export function getInitials(fullName: string): string {
  if (!fullName) {
    return "";
  }
  return fullName
    .trim()
    .split(/\s+/)
    .map((word) => word[0]?.toUpperCase() || "")
    .join("");
}

export const teamMembersTagData = [
    { label: "All", state: "active" },
    { label: "Staff", state: "default", status: "STAFF" },
    { label: "Admin", state: "default", status: "ADMIN" },
  ];

export const roleOptions = [
  { label: "STAFF", value: "STAFF" },
  { label: "ADMIN", value: "ADMIN" },
  { label: "OWNER", value: "OWNER" },
];
