import { AppTheme, RDSButton, RDSEmptyState, RDSModal } from "@roshn/ui-kit";
import { roleOptions } from "./constants";
import { useState } from "react";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Input } from "~/components/form-components/input/input";
import { Select } from "~/components/form-components/select/select";
import { useTheme } from "@emotion/react";
import { useInviteMember } from "~/services/team-members/hooks/use-invite-member";

export type InviteMemberModalProps = {
  showModal: boolean;
  onClose: () => void;
};

const inviteMemberSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }).trim(),
  role: z.string().min(1, { message: "Role is required" }),
});

export default function InviteMemberModal({ showModal, onClose }: InviteMemberModalProps) {
  const theme = useTheme() as AppTheme;
  const [showSuccess, setShowSuccess] = useState(false);

  const {
    control,
    getValues,
    formState: { isValid },
  } = useForm({
    resolver: zodResolver(inviteMemberSchema),
    mode: "onChange",
    defaultValues: { email: "", role: "" },
  });

  const { mutateAsync, isPending } = useInviteMember();

  const handleSubmit = () => {
    const payload = {
      email: getValues("email"),
      role: getValues("role"),
    };
    mutateAsync(
      { payload },
      {
        onSuccess: () => {
          setShowSuccess(true);
        },
      },
    );
  };

  return (
    <>
      {!showSuccess ? (
        <RDSModal
          showContent
          headerProps={{
            label: "Invite member",
            trailIcon: true,
            trailIconProps: {
              onClick: onClose,
            },
            type: "centred",
            leadIcon: false,
            hasAsset: false,
          }}
          isOpen={showModal}
          showDescription={false}
          content={
            <div
              css={{ display: "flex", flexDirection: "column", gap: theme?.rds?.dimension["300"] }}
            >
              <div css={{ display: "flex", gap: "1rem" }}>
                <Input
                  css={{ width: "100%" }}
                  control={control}
                  label="Email address"
                  name="email"
                  placeholder="Email address"
                  isRequired
                />
                <Select
                  control={control}
                  options={roleOptions}
                  label="Role"
                  name="role"
                  isRequired
                  // onChange={handleInputChange}
                />
              </div>
              <RDSButton
                disabled={!isValid}
                variant="primary"
                size="lg"
                text="SEND INVITE"
                onClick={handleSubmit}
                loading={isPending}
              />
            </div>
          }
        />
      ) : (
        <RDSModal
          isOpen={showSuccess}
          showDescription
          showContent
          content={
            <RDSEmptyState
              appearance="success"
              size="sm"
              body="An invitation link has been <NAME_EMAIL> to join your organization. The link will expire in 2 days."
              heading="Invitation sent!"
              buttons={[
                {
                  text: "BACK TO TEAM MEMBERS",
                  variant: "primary",
                  css: { textTransform: "none" },
                  onClick: () => {
                    setShowSuccess(false);
                    onClose();
                  },
                },
              ]}
            />
          }
        />
      )}
    </>
  );
}
