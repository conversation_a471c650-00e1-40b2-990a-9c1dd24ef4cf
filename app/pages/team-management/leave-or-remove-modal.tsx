import { AppTheme, RDSEmptyState, RDSModal } from "@roshn/ui-kit";
import { MemberData } from "./constants";
import { useTheme } from "@emotion/react";
import { useRemoveMember } from "~/services/team-members/hooks/use-remove-member";

export type LeaveOrRemoveModalProps = {
  showModal: boolean;
  onClose: () => void;
  actionType: "REMOVE" | "CANCEL_INVITE" | "RESEND_INVITE" | "LEAVE_TEAM";
  memberData: MemberData | null;
};

export default function LeaveOrRemoveModal({
  showModal,
  onClose,
  actionType,
  memberData,
}: LeaveOrRemoveModalProps) {
  const theme = useTheme() as AppTheme;

  const { mutateAsync: removeMember, isPending: isRemovePending } = useRemoveMember({});

  const getModalConfig = () => {
    switch (actionType) {
      case "REMOVE":
        return {
          heading: "Remove Team Member",
          body: `Are you sure you want to remove ${memberData?.name} from the team? Their access will be removed immediately`,
          buttonPrimaryText: "YES, REMOVE MEMBER",
          buttonSecondaryText: "NO, KEEP MEMBER",
          buttonVariant: "primary" as const,
        };
      case "CANCEL_INVITE":
        return {
          heading: "Cancel Invitation",
          body: `Are you sure you want to cancel the invitation for ${memberData?.email}?`,
          buttonPrimaryText: "YES, CANCEL INVITATION",
          buttonSecondaryText: "NO, KEEP INVITATION",
          buttonVariant: "primary" as const,
        };
      case "RESEND_INVITE":
        return {
          heading: "Resend Invitation",
          body: `Resend invitation to ${memberData?.email}?`,
          buttonPrimaryText: "YES, RESEND INVITE",
          buttonSecondaryText: "NO, KEEP INVITE",
          buttonVariant: "primary" as const,
        };
      case "LEAVE_TEAM":
        return {
          heading: "Leave the Team",
          body: "Are you sure you want to leave [Organization Name]? You’ll lose access immediately and will need a new invitation to rejoin.",
          buttonPrimaryText: "YES, LEAVE TEAM",
          buttonSecondaryText: "NO, STAY ON TEAM",
          buttonVariant: "primary" as const,
        };
      default:
        return {
          heading: "Action",
          body: "Please confirm your action.",
          buttonPrimaryText: "YES, CONFIRM",
          buttonSecondaryText: "NO, CANCEL",
          buttonVariant: "primary" as const,
        };
    }
  };

  const config = getModalConfig();

  const handlePrimaryAction = () => {
    removeMember({ memberId: memberData?.id }, {
        onSuccess: () => {
          onClose();
        },
    });
  };

  return (
    <>
      <RDSModal
        appearance="danger"
        showContent
        headerProps={{
          label: config.title,
          trailIcon: true,
          trailIconProps: {
            onClick: onClose,
          },
          type: "centred",
          leadIcon: false,
          hasAsset: false,
        }}
        isOpen={showModal}
        showDescription={false}
        heading={config.heading}
        body={config.body}
        content={
          <>
            <RDSEmptyState
              appearance="danger"
              size="sm"
              body={config.body}
              heading={config.heading}
              buttons={[
                {
                  text: config.buttonPrimaryText,
                  variant: "primary",
                  css: { textTransform: "none" },
                  loading: isRemovePending,
                  onClick: () => {
                    handlePrimaryAction();
                  },
                },
                {
                  variant: "secondary",
                  text: config.buttonSecondaryText,
                  onClick: () => onClose(),
                },
              ]}
            />
          </>
        }
      />
    </>
  );
}
