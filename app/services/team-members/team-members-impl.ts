import { inject, injectable } from "inversify";

import { RestHelper } from "~/services/rest-helper";
import { InviteMemberPayload, TeamMemberListArgs, TeamMembersServiceInterface } from "./team-members";

@injectable()
export class TeamMemberImpl implements TeamMembersServiceInterface {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
  ) {}

  getTeamMembers: (args: TeamMemberListArgs) => Promise<any> = (args: TeamMemberListArgs) => {
    return this.restHelper.get("/api/v1.5/teams/members/", {
      params: args,
    });
  };

  inviteMember: (payload: InviteMemberPayload) => Promise<any> = (payload: InviteMemberPayload) => {
    return this.restHelper.post("/api/v1/teams/members/", {
      data: payload,
    });
  };

  getMyInfo: () => Promise<any> = () => {
    return this.restHelper.get("/api/v1/teams/members/me/");
  };

  removeMember: (memberId: string) => Promise<any> = (memberId: string) => {
    return this.restHelper.delete(`/api/v1/teams/members/${memberId}/`);
  };

  resendInvite: (memberId: string) => Promise<any> = (memberId: string) => {
    return this.restHelper.post(`/api/v1/teams/members/${memberId}/resend-invite/`);
  };
}
