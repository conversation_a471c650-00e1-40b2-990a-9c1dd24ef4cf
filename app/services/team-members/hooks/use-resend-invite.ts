import { useMutation, UseMutationOptions } from "@tanstack/react-query";

import { useInjection } from "~/hooks/use-di";
import { TeamMemberService } from "~/services/team-members/team-members";

export function useResendInvite(
  options?: UseMutationOptions<
    any,
    unknown,
    {
      memberId: string;
    }
  >,
) {
  const teamMemberService = useInjection<TeamMemberService>(TeamMemberService);

  return useMutation({
    mutationFn: ({ memberId }: { memberId: string }) => teamMemberService.resendInvite(memberId),
    ...options,
  });
}
