import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { TeamMemberService } from "~/services/team-members/team-members";

export function useRemoveMember(
  options?: UseMutationOptions<
    any,
    unknown,
    {
      memberId: string;
    }
  >,
) {
  const teamMemberService = useInjection<TeamMemberService>(TeamMemberService);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ memberId }: { memberId: string }) => teamMemberService.removeMember(memberId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKey.TEAM_MEMBERS] });
    },
    ...options,
  });
}
