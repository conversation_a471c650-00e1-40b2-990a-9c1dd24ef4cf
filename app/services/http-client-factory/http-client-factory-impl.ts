/* eslint-disable @typescript-eslint/no-explicit-any */
import { ForgeRockBridge } from "@roshn/shared/forgerock-bridge";
import axios, {
  Axios,
  InternalAxiosRequestConfig,
  AxiosInstance,
  AxiosResponse,
  AxiosError,
  HttpStatusCode,
} from "axios";
import { inject, injectable } from "inversify";
import mitt from "mitt";

import { getItem, setItem } from "~/utils/storage-helpers";

import { EnvService } from "../env";

import {
  Events,
  HttpClient,
  HttpClientFactory,
  HttpRequestConfig,
  HttpResponse,
} from "./http-client-factory";

export class HttpClientImpl implements HttpClient {
  constructor(readonly axios: Axios) {}

  request<T = any, R = HttpResponse<T, any>, D = any>(config: HttpRequestConfig<D>): Promise<R> {
    return this.axios.request(config);
  }
  get<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.get(url, config);
  }
  delete<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.delete(url, config);
  }
  head<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.head(url, config);
  }
  options<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.options(url, config);
  }
  post<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    data?: D | undefined,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.post(url, data, config);
  }
  put<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    data?: D | undefined,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.put(url, data, config);
  }
  patch<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    data?: D | undefined,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.patch(url, data, config);
  }
}

export type TokenGenerator = () => Promise<string | undefined>;

@injectable()
export class HttpClientFactoryImpl implements HttpClientFactory {
  static create(config?: HttpRequestConfig<any> | undefined) {
    const create =
      typeof axios.create === "function" ? axios.create : (axios as any).default.create;

    return create({
      ...config,
    }) as AxiosInstance;
  }

  private readonly mitt = mitt<Events>();

  constructor(
    @inject<ForgeRockBridge>(ForgeRockBridge)
    private readonly forgeRockBridge: ForgeRockBridge,

    @inject<EnvService>(EnvService)
    private readonly envService: EnvService,
  ) {} // private readonly forgeRockBridge: ForgeRockBridge // @inject<ForgeRockBridge>(ForgeRockBridge)

  private readonly emitUnauthenticated = () => {
    this.mitt.emit("unauthenticated");
  };

  private readonly tokenInjector = async (
    config: InternalAxiosRequestConfig,
  ): Promise<InternalAxiosRequestConfig> => {
    config.headers = config.headers || {};
    if (this.envService.AUTH_TYPE === "SHOP_BOXO") {
      const accessToken = getItem("accessToken");
      if (accessToken) {
        config.headers["Authorization"] = `Bearer ${accessToken}`;
      }
    } else {
      const oauth2token = await this.forgeRockBridge.getTokens();

      if (!oauth2token) {
        this.emitUnauthenticated();
        throw new Error("No token found");
      }

      config.headers["Authorization"] = `Bearer ${oauth2token.accessToken}`;
    }

    return config;
  };

  private readonly refreshAccessToken = async (): Promise<string | undefined> => {
    const refreshToken = getItem("refreshToken");
    const response = await axios.post(
      `${this.envService.APP_API_URL}/api/v1/accounts/refresh_token/`,
      { refresh: refreshToken },
    );
    const newAccessToken = response.data.access;
    const newRefreshToken = response.data.refresh;
    setItem("accessToken", newAccessToken);
    setItem("refreshToken", newRefreshToken);
    return newAccessToken;
  };

  private readonly unauthenticatedInterceptor = async (error: AxiosError) => {
    const response = error.response as AxiosResponse;
    const originalRequest = error.config;
    if (response?.status === HttpStatusCode.Unauthorized || response?.status === HttpStatusCode.Forbidden) {
      if (
        this.envService.AUTH_TYPE === "SHOP_BOXO" &&
        originalRequest &&
        !(originalRequest as any)._retry
      ) {
        (originalRequest as any)._retry = true;
        try {
          const newAccessToken = await this.refreshAccessToken();
          if (newAccessToken) {
            originalRequest.headers = originalRequest.headers || {};
            originalRequest.headers["Authorization"] = `Bearer ${newAccessToken}`;
            return axios(originalRequest);
          }
          this.emitUnauthenticated();
          return Promise.reject(error);
        } catch (refreshError) {
          this.emitUnauthenticated();
          return Promise.reject(refreshError);
        }
      } else {
        this.emitUnauthenticated();
      }
    }
    return Promise.reject(error);
  };

  on: <E extends "unauthenticated">(event: E, listener: (event: Events[E]) => void) => this = (
    event,
    listener,
  ) => {
    this.mitt.on(event, listener);
    return this;
  };

  off: <E extends "unauthenticated">(event: E, listener: (event: Events[E]) => void) => this = (
    event,
    listener,
  ) => {
    this.mitt.off(event, listener);

    return this;
  };

  create(config?: HttpRequestConfig<any> | undefined): HttpClient {
    const instance = HttpClientFactoryImpl.create({
      ...config,
    });

    instance.interceptors.request.use(this.tokenInjector);

    instance.interceptors.response.use(undefined, this.unauthenticatedInterceptor);

    return new HttpClientImpl(instance);
  }
}

/**
 * Does not depend on ForgeRockBridge, for reducing testing boilerplate.
 */
@injectable()
export class HttpClientFactoryTestImpl implements HttpClientFactory {
  private readonly mitt = mitt<Events>();

  on: <E extends "unauthenticated">(event: E, listener: (event: Events[E]) => void) => this = (
    event,
    listener,
  ) => {
    this.mitt.on(event, listener);
    return this;
  };

  off: <E extends "unauthenticated">(event: E, listener: (event: Events[E]) => void) => this = (
    event,
    listener,
  ) => {
    this.mitt.off(event, listener);

    return this;
  };

  create(config?: HttpRequestConfig<any> | undefined): HttpClient {
    const instance = HttpClientFactoryImpl.create({
      ...config,
    });

    return new HttpClientImpl(instance);
  }
}
